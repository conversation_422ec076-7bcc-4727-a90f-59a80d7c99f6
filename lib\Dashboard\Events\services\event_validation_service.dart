import '../models/event_isar.dart';
import '../models/event_type_isar.dart';
import '../models/event_enums.dart';

/// Event validation service for form validation and business rules
class EventValidationService {
  /// Validate event data before saving
  static Map<String, String> validateEvent(EventIsar event) {
    final errors = <String, String>{};

    // Title validation
    if (event.title.trim().isEmpty) {
      errors['title'] = 'Event title is required';
    } else if (event.title.length > 100) {
      errors['title'] = 'Event title must be 100 characters or less';
    }

    // Description validation
    if (event.description != null && event.description!.length > 500) {
      errors['description'] = 'Description must be 500 characters or less';
    }

    // Cattle tag validation
    if (event.cattleTagId.trim().isEmpty) {
      errors['cattleTagId'] = 'Cattle selection is required';
    }

    // Event type validation
    if (event.eventTypeId.trim().isEmpty) {
      errors['eventTypeId'] = 'Event type is required';
    }

    // Scheduled date validation
    if (event.scheduledDate == null) {
      errors['scheduledDate'] = 'Scheduled date is required';
    } else {
      // Check if scheduled date is too far in the past
      final now = DateTime.now();
      final daysDifference = now.difference(event.scheduledDate!).inDays;
      if (daysDifference > 365) {
        errors['scheduledDate'] = 'Scheduled date cannot be more than 1 year in the past';
      }
    }

    // Cost validation
    if (event.estimatedCost != null && event.estimatedCost! < 0) {
      errors['estimatedCost'] = 'Estimated cost cannot be negative';
    }

    if (event.actualCost != null && event.actualCost! < 0) {
      errors['actualCost'] = 'Actual cost cannot be negative';
    }

    // Recurring event validation
    if (event.isRecurring && event.recurringPattern == null) {
      errors['recurringPattern'] = 'Recurring pattern is required for recurring events';
    }

    return errors;
  }

  /// Validate event type data
  static Map<String, String> validateEventType(EventTypeIsar eventType) {
    final errors = <String, String>{};

    // Name validation
    if (eventType.name.trim().isEmpty) {
      errors['name'] = 'Event type name is required';
    } else if (eventType.name.length > 50) {
      errors['name'] = 'Event type name must be 50 characters or less';
    }

    // Description validation
    if (eventType.description != null && eventType.description!.length > 200) {
      errors['description'] = 'Description must be 200 characters or less';
    }

    // Default duration validation
    if (eventType.defaultDurationMinutes != null && eventType.defaultDurationMinutes! <= 0) {
      errors['defaultDurationMinutes'] = 'Default duration must be greater than 0';
    }

    // Default cost validation
    if (eventType.defaultEstimatedCost != null && eventType.defaultEstimatedCost! < 0) {
      errors['defaultEstimatedCost'] = 'Default estimated cost cannot be negative';
    }

    return errors;
  }

  /// Check if event can be completed
  static bool canCompleteEvent(EventIsar event) {
    return event.status != EventStatus.completed && 
           event.status != EventStatus.cancelled;
  }

  /// Check if event can be cancelled
  static bool canCancelEvent(EventIsar event) {
    return event.status != EventStatus.completed && 
           event.status != EventStatus.cancelled;
  }

  /// Check if event can be edited
  static bool canEditEvent(EventIsar event) {
    return event.status != EventStatus.completed;
  }

  /// Check if event can be deleted
  static bool canDeleteEvent(EventIsar event) {
    return true; // Events can always be deleted for record keeping
  }

  /// Validate business rules for event scheduling
  static List<String> validateEventScheduling(EventIsar event, List<EventIsar> existingEvents) {
    final warnings = <String>[];

    if (event.scheduledDate == null) return warnings;

    // Check for overlapping events for the same cattle
    final overlappingEvents = existingEvents.where((existing) {
      if (existing.businessId == event.businessId) return false; // Skip self
      if (existing.cattleTagId != event.cattleTagId) return false; // Different cattle
      if (existing.scheduledDate == null) return false;

      // Check if events are on the same day
      final existingDate = existing.scheduledDate!;
      final eventDate = event.scheduledDate!;
      
      return existingDate.year == eventDate.year &&
             existingDate.month == eventDate.month &&
             existingDate.day == eventDate.day;
    }).toList();

    if (overlappingEvents.isNotEmpty) {
      warnings.add('This cattle has ${overlappingEvents.length} other event(s) scheduled on the same day');
    }

    // Check for high-priority events
    final highPriorityEvents = overlappingEvents.where((e) => e.priority == EventPriority.high).toList();
    if (highPriorityEvents.isNotEmpty) {
      warnings.add('High priority events are already scheduled for this cattle on this day');
    }

    return warnings;
  }
}
