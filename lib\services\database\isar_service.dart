import 'dart:io';
import 'package:isar/isar.dart';
import 'package:path_provider/path_provider.dart';
import 'package:logging/logging.dart';
import '../../Dashboard/Cattle/models/cattle_isar.dart';
import '../../Dashboard/Farm Setup/models/animal_type_isar.dart';
import '../../Dashboard/Farm Setup/models/breed_category_isar.dart';
import '../../Dashboard/Farm Setup/models/farm_isar.dart';
import '../../Dashboard/Farm Setup/models/user_role_isar.dart';
import '../../Dashboard/Breeding/models/breeding_record_isar.dart';
import '../../Dashboard/Breeding/models/pregnancy_record_isar.dart';
import '../../Dashboard/Breeding/models/breeding_event_isar.dart';
import '../../Dashboard/Breeding/models/delivery_record_isar.dart';
import '../../Dashboard/Transactions/models/transaction_isar.dart';
import '../../Dashboard/Transactions/models/category_isar.dart';
import '../../Dashboard/Health/models/health_record_isar.dart';
import '../../Dashboard/Health/models/vaccination_record_isar.dart';
import '../../Dashboard/Milk Records/models/milk_record_isar.dart';
import '../../Dashboard/Milk Records/models/milk_sale_isar.dart';
import '../../Dashboard/Weight/models/weight_record_isar.dart';
import '../../Dashboard/Notifications/models/notification_isar.dart';
import '../../Dashboard/Notifications/models/notification_settings_isar.dart';
import '../../Dashboard/Notifications/models/notification_operation.dart';

import '../../Dashboard/Events/models/event_isar.dart';
import '../../Dashboard/Events/models/event_type_isar.dart';
import '../../Dashboard/Events/models/event_attachment_isar.dart';
import '../../Dashboard/Farm Setup/models/backup_settings_isar.dart';
import '../../Dashboard/Farm Setup/models/currency_settings_isar.dart';
import '../../Dashboard/Farm Setup/models/milk_settings_isar.dart';
import '../../Dashboard/Health/models/medication_isar.dart';
import '../../Dashboard/Health/models/treatment_isar.dart';
import '../../Dashboard/Farm Setup/models/alert_settings_isar.dart';
import '../../Dashboard/Farm Setup/models/farm_user_isar.dart';

import '../../Dashboard/User Account/models/user_isar.dart';
import '../../Dashboard/User Account/models/user_session_isar.dart';
import '../../Dashboard/User Account/models/user_settings_isar.dart';

import 'package:flutter/foundation.dart';
// Temporarily commented out until properly generated
// import '../../Dashboard/Farm Setup/models/animal_stage_isar.dart';

// Generated schema files are imported through their main dart files

/// Service to manage the Isar database instance
class IsarService {
  final Logger _logger = Logger('IsarService');
  Isar? _isar;
  static const String _databaseName = 'cattle_manager_db';
  bool _isInitialized = false;

  // Public constructor for dependency injection
  IsarService();

  /// Check if Isar has been initialized
  bool get isInitialized => _isInitialized;

  /// Get the Isar instance
  Isar get isar {
    if (!_isInitialized || _isar == null) {
      throw Exception('Isar database not initialized');
    }
    return _isar!;
  }

  /// Initialize Isar database
  Future<void> initialize() async {
    debugPrint('🗄️ [DB_DEBUG] IsarService.initialize() called at ${DateTime.now()}');

    if (_isInitialized) {
      debugPrint('🗄️ [DB_DEBUG] Isar database already initialized, skipping');
      _logger.info('Isar database already initialized');
      return;
    }

    try {
      debugPrint('🗄️ [DB_DEBUG] Starting Isar database initialization...');
      final dir = await getApplicationDocumentsDirectory();
      debugPrint('🗄️ [DB_DEBUG] Database directory: $dir.path');
      _logger.info('Initializing Isar database at $dir.path');

      // You need to run 'flutter pub run build_runner build' to generate the schemas
      debugPrint('🗄️ [DB_DEBUG] Opening Isar database with schemas...');
      _isar = await Isar.open(
        [
          CattleIsarSchema,
          AnimalTypeIsarSchema,
          BreedCategoryIsarSchema,
          // Temporarily commented out until properly generated
          // AnimalStageIsarSchema,
          FarmIsarSchema,
          UserRoleIsarSchema,
          BreedingRecordIsarSchema,
          PregnancyRecordIsarSchema,
          BreedingEventIsarSchema,
          DeliveryRecordIsarSchema,
          TransactionIsarSchema,
          CategoryIsarSchema,
          HealthRecordIsarSchema,
          VaccinationIsarSchema,
          MedicationIsarSchema,
          TreatmentIsarSchema,
          MilkRecordIsarSchema,
          MilkSaleIsarSchema,
          WeightRecordIsarSchema,
          WeightGoalIsarSchema,
          NotificationIsarSchema,
          NotificationSettingsIsarSchema,
          NotificationOperationSchema,

          EventIsarSchema,
          EventTypeIsarSchema,
          // EventCategoryIsarSchema,
          // EventRecurrenceIsarSchema,
          EventAttachmentIsarSchema,
          BackupSettingsIsarSchema,
          CurrencySettingsIsarSchema,
          MilkSettingsIsarSchema,
          AlertSettingsIsarSchema,
          FarmUserIsarSchema,

          UserIsarSchema,
          UserSessionIsarSchema,
          UserSettingsIsarSchema,

        ],
        directory: dir.path,
        name: _databaseName,
        inspector: true, // Enable inspector in debug mode
      );

      _isInitialized = true;
      debugPrint('✅ [DB_DEBUG] Isar database opened successfully');
      debugPrint('🗄️ [DB_DEBUG] Database name: $_databaseName');
      debugPrint('🗄️ [DB_DEBUG] Database path: $dir.path/$_databaseName');
      debugPrint('🗄️ [DB_DEBUG] Inspector enabled: true');

      _logger.info('Isar database initialized successfully');
    } catch (e) {
      debugPrint('❌ [DB_DEBUG] Error initializing Isar database: $e');
      debugPrint('❌ [DB_DEBUG] Error type: $e.runtimeType');
      debugPrint('❌ [DB_DEBUG] Stack trace: $StackTrace.current');

      _logger.severe('Error initializing Isar database: $e');
      rethrow;
    }
  }

  /// Close the Isar database instance
  Future<void> close() async {
    if (_isInitialized && _isar != null) {
      try {
        await _isar!.close();
        _isar = null;
        _isInitialized = false;
        _logger.info('Isar database closed successfully');
      } catch (e) {
        _logger.severe('Error closing Isar database: $e');
        rethrow;
      }
    }
  }

  /// Clear all data from the database
  Future<void> clearAllData() async {
    return _handleDatabaseOperation(() async {
      await _isar!.writeTxn(() async {
        await _isar!.clear();
      });
      _logger.info('All data cleared from Isar database');
    }, 'clearing all data from Isar database');
  }

  /// Create a backup of the database using Isar's safe hot backup feature
  Future<File> backup() async {
    return _handleDatabaseOperation(() async {
      final backupPath = await _getBackupPath();
      await _isar!.writeTxn(() async {
        // Use Isar's native hot backup - no need to close the database!
        await _isar!.copyToFile(backupPath);
      });
      _logger.info('Database backup created at: $backupPath');
      return File(backupPath);
    }, 'creating database backup');
  }

  /// Helper method to generate backup file path
  Future<String> _getBackupPath() async {
    final appDir = await getApplicationDocumentsDirectory();
    final now = DateTime.now();
    final formattedDate =
        '$now.year-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}-${now.hour.toString().padLeft(2, '0')}-${now.minute.toString().padLeft(2, '0')}';
    return '$appDir.path/backup_$formattedDate.isar';
  }

  /// Helper method for consistent database operation error handling
  Future<T> _handleDatabaseOperation<T>(
    Future<T> Function() operation,
    String operationName,
  ) async {
    if (!_isInitialized) {
      await initialize();
    }

    try {
      return await operation();
    } catch (e) {
      _logger.severe('Error $operationName: $e');
      rethrow;
    }
  }

  /// Restore from a backup file
  Future<void> restoreFromBackup(File backupFile) async {
    try {
      // Close the current instance if it's open
      if (_isInitialized && _isar != null) {
        await _isar!.close();
        _isar = null;
        _isInitialized = false;
      }

      final appDir = await getApplicationDocumentsDirectory();
      final dbFilePath = '$appDir.path/$_databaseName.isar';

      // Copy the backup file to the database location
      await backupFile.copy(dbFilePath);

      // Reopen the database
      await initialize();

      _logger.info('Database restored from backup: $backupFile.path');
    } catch (e) {
      _logger.severe('Error restoring database from backup: $e');
      // Make sure to reinitialize if there was an error
      if (!_isInitialized) {
        try {
          await initialize();
        } catch (initError) {
          _logger.severe('Failed to reinitialize database after restore error: $initError');
        }
      }
      rethrow;
    }
  }

  // Isar getter is already defined above

  // Collection accessors
  IsarCollection<CattleIsar> get cattleIsars => _isar!.collection<CattleIsar>();
  IsarCollection<AnimalTypeIsar> get animalTypeIsars => _isar!.collection<AnimalTypeIsar>();
  IsarCollection<BreedCategoryIsar> get breedCategoryIsars => _isar!.collection<BreedCategoryIsar>();
  IsarCollection<FarmIsar> get farmIsars => _isar!.collection<FarmIsar>();
  IsarCollection<UserRoleIsar> get userRoleIsars => _isar!.collection<UserRoleIsar>();
  IsarCollection<BreedingRecordIsar> get breedingRecordIsars => _isar!.collection<BreedingRecordIsar>();
  IsarCollection<PregnancyRecordIsar> get pregnancyRecordIsars => _isar!.collection<PregnancyRecordIsar>();
  IsarCollection<BreedingEventIsar> get breedingEventIsars => _isar!.collection<BreedingEventIsar>();
  IsarCollection<DeliveryRecordIsar> get deliveryRecordIsars => _isar!.collection<DeliveryRecordIsar>();
  IsarCollection<TransactionIsar> get transactionIsars => _isar!.collection<TransactionIsar>();
  IsarCollection<CategoryIsar> get categoryIsars => _isar!.collection<CategoryIsar>();
  IsarCollection<HealthRecordIsar> get healthRecordIsars => _isar!.collection<HealthRecordIsar>();
  IsarCollection<VaccinationIsar> get vaccinationIsars => _isar!.collection<VaccinationIsar>();
  IsarCollection<MedicationIsar> get medicationIsars => _isar!.collection<MedicationIsar>();
  IsarCollection<TreatmentIsar> get treatmentIsars => _isar!.collection<TreatmentIsar>();
  IsarCollection<MilkRecordIsar> get milkRecordIsars => _isar!.collection<MilkRecordIsar>();
  IsarCollection<MilkSaleIsar> get milkSaleIsars => _isar!.collection<MilkSaleIsar>();
  IsarCollection<WeightRecordIsar> get weightRecordIsars => _isar!.collection<WeightRecordIsar>();
  IsarCollection<WeightGoalIsar> get weightGoalIsars => _isar!.collection<WeightGoalIsar>();
  IsarCollection<NotificationIsar> get notificationIsars => _isar!.collection<NotificationIsar>();
  IsarCollection<NotificationSettingsIsar> get notificationSettingsIsars => _isar!.collection<NotificationSettingsIsar>();
  IsarCollection<NotificationOperation> get notificationOperations => _isar!.collection<NotificationOperation>();

  IsarCollection<EventIsar> get eventIsars => _isar!.collection<EventIsar>();
  IsarCollection<BackupSettingsIsar> get backupSettingsIsars => _isar!.collection<BackupSettingsIsar>();
  IsarCollection<CurrencySettingsIsar> get currencySettingsIsars => _isar!.collection<CurrencySettingsIsar>();
  IsarCollection<MilkSettingsIsar> get milkSettingsIsars => _isar!.collection<MilkSettingsIsar>();
  IsarCollection<AlertSettingsIsar> get alertSettingsIsars => _isar!.collection<AlertSettingsIsar>();
  IsarCollection<FarmUserIsar> get farmUserIsars => _isar!.collection<FarmUserIsar>();

  IsarCollection<UserIsar> get userIsars => _isar!.collection<UserIsar>();
  IsarCollection<UserSessionIsar> get userSessionIsars => _isar!.collection<UserSessionIsar>();
  IsarCollection<UserSettingsIsar> get userSettingsIsars => _isar!.collection<UserSettingsIsar>();

  // Temporarily commented out until properly generated
  // IsarCollection<AnimalStageIsar> get animalStageIsars => _isar.collection<AnimalStageIsar>();
}
