import 'package:isar/isar.dart';

import '../models/health_record_isar.dart';
import '../models/medication_isar.dart';
import '../models/treatment_isar.dart';
import '../models/vaccination_record_isar.dart';
import '../../../services/database/isar_service.dart';

import 'package:flutter/foundation.dart';
// Legacy alias for compatibility
typedef HealthHandler = HealthRepository;

/// Pure reactive repository for Health module database operations
/// Following the cattle/weight module pattern: Stream-only, logic-free, simple CRUD
/// Maximum purity: no error handling, no logging - exceptions bubble up naturally
/// Sync functionality moved to dedicated HealthSyncService for clean separation
class HealthRepository {
  final IsarService _isarService;

  // Public constructor with explicit dependency injection
  HealthRepository(this._isarService);

  // Getter for Isar instance
  Isar get _isar => _isarService.isar;

  //=== REACTIVE HEALTH STREAMS ===//

  /// Watches all health records with reactive updates
  /// The controller is responsible for all filtering and sorting
  Stream<List<HealthRecordIsar>> watchAllHealthRecords() {
    return _isar.healthRecordIsars.where().watch(fireImmediately: true);
  }

  /// Watches all medications with reactive updates
  /// The controller is responsible for all filtering and sorting
  Stream<List<MedicationIsar>> watchAllMedications() {
    return _isar.medicationIsars.where().watch(fireImmediately: true);
  }

  /// Watches all treatments with reactive updates
  /// The controller is responsible for all filtering and sorting
  Stream<List<TreatmentIsar>> watchAllTreatments() {
    return _isar.treatmentIsars.where().watch(fireImmediately: true);
  }

  /// Watches all vaccinations with reactive updates
  /// The controller is responsible for all filtering and sorting
  Stream<List<VaccinationIsar>> watchAllVaccinations() {
    return _isar.vaccinationIsars.where().watch(fireImmediately: true);
  }

  //=== HEALTH RECORDS CRUD ===//

  /// Save (add or update) a health record using Isar's native upsert
  Future<void> saveHealthRecord(HealthRecordIsar record) async {
    await _isar.writeTxn(() async {
      await _isar.healthRecordIsars.put(record);
    });
  }

  /// Delete a health record by its Isar ID
  Future<void> deleteHealthRecord(int id) async {
    await _isar.writeTxn(() async {
      await _isar.healthRecordIsars.delete(id);
    });
  }

  //=== MEDICATION CRUD ===//

  /// Save (add or update) a medication using Isar's native upsert
  Future<void> saveMedication(MedicationIsar medication) async {
    await _isar.writeTxn(() async {
      await _isar.medicationIsars.put(medication);
    });
  }

  /// Delete a medication by its Isar ID
  Future<void> deleteMedication(int id) async {
    await _isar.writeTxn(() async {
      await _isar.medicationIsars.delete(id);
    });
  }

  //=== TREATMENT CRUD ===//

  /// Save (add or update) a treatment using Isar's native upsert
  Future<void> saveTreatment(TreatmentIsar treatment) async {
    await _isar.writeTxn(() async {
      await _isar.treatmentIsars.put(treatment);
    });
  }

  /// Delete a treatment by its Isar ID
  Future<void> deleteTreatment(int id) async {
    await _isar.writeTxn(() async {
      await _isar.treatmentIsars.delete(id);
    });
  }

  //=== VACCINATION CRUD ===//

  /// Save (add or update) a vaccination using Isar's native upsert
  Future<void> saveVaccination(VaccinationIsar vaccination) async {
    await _isar.writeTxn(() async {
      await _isar.vaccinationIsars.put(vaccination);
    });
  }

  /// Delete a vaccination by its Isar ID
  Future<void> deleteVaccination(int id) async {
    await _isar.writeTxn(() async {
      await _isar.vaccinationIsars.delete(id);
    });
  }

  //=== QUERY METHODS FOR ANALYTICS ===//

  /// Get health records for a specific cattle (for analytics)
  /// Returns a Future<List> for one-time data fetching
  Future<List<HealthRecordIsar>> getHealthRecordsByCattleId(String cattleId) async {
    return await _isar.healthRecordIsars
        .filter()
        .cattleTagIdEqualTo(cattleId)
        .findAll();
  }

  /// Get all health records (for analytics and validation)
  /// Returns a Future<List> for one-time data fetching
  Future<List<HealthRecordIsar>> getAllHealthRecords() async {
    return await _isar.healthRecordIsars.where().findAll();
  }

  /// Get all medications (for analytics and validation)
  /// Returns a Future<List> for one-time data fetching
  Future<List<MedicationIsar>> getAllMedications() async {
    return await _isar.medicationIsars.where().findAll();
  }

  /// Get all treatments (for analytics and validation)
  /// Returns a Future<List> for one-time data fetching
  Future<List<TreatmentIsar>> getAllTreatments() async {
    return await _isar.treatmentIsars.where().findAll();
  }

  /// Get all vaccinations (for analytics and validation)
  /// Returns a Future<List> for one-time data fetching
  Future<List<VaccinationIsar>> getAllVaccinations() async {
    return await _isar.vaccinationIsars.where().findAll();
  }
}


