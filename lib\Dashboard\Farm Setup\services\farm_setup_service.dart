import 'package:uuid/uuid.dart';
import 'package:logging/logging.dart';
import '../models/farm_isar.dart';
import '../models/breed_category_isar.dart';
import '../models/animal_type_isar.dart';
import '../models/alert_settings_isar.dart';
import '../models/backup_settings_isar.dart';
import '../models/currency_settings_isar.dart';
import '../../Transactions/models/category_isar.dart';
import '../../Events/models/event_type_isar.dart';
import 'farm_repository.dart';
import 'breed_category_repository.dart';
import 'cloud_backup_service.dart';
import '../../../core/base/base_service.dart';
import '../../../services/database/exceptions/database_exceptions.dart';

/// Business logic service for Farm Setup operations
/// Handles all complex operations extracted from the old FarmSetupRepository
/// Following the established pattern: business logic in services, not repositories
class FarmSetupService extends BaseService {
  final FarmRepository _farmRepository;
  final BreedCategoryRepository _breedCategoryRepository;
  final CloudBackupService _cloudBackupService;

  FarmSetupService(
    this._farmRepository,
    this._breedCategoryRepository,
    this._cloudBackupService,
  ) : super('FarmSetupService');

  //=== FARM OPERATIONS ===//

  /// Create a new farm with validation and default setup
  Future<ServiceResult<FarmIsar>> createFarm({
    required String name,
    required String ownerName,
    required String ownerContact,
    required String ownerEmail,
    String? location,
    String? address,
    double? latitude,
    double? longitude,
    String? farmType,
    int? cattleCount,
    int? capacity,
  }) async {
    return await executeWithErrorHandling(
      'create farm',
      () async {
        // Validate required fields
        if (name.isEmpty) {
          throw ValidationException('Farm name is required');
        }
        if (ownerName.isEmpty) {
          throw ValidationException('Owner name is required');
        }
        if (ownerContact.isEmpty) {
          throw ValidationException('Owner contact is required');
        }

        // Create farm with generated business ID
        final farm = FarmIsar.create(
          id: const Uuid().v4(),
          name: name,
          ownerName: ownerName,
          ownerContact: ownerContact,
          ownerEmail: ownerEmail,
          latitude: latitude,
          longitude: longitude,
          address: address,
          farmType: farmType ?? 'mixed',
          cattleCount: cattleCount ?? 0,
          capacity: capacity ?? 100,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // Set location if provided
        if (location != null) {
          farm.location = location;
        }

        // Save the farm
        await _farmRepository.save(farm);

        // Create default categories and settings
        await _createDefaultFarmSetup(farm.farmBusinessId!);

        logInfo('Created farm: ${farm.name} (ID: ${farm.farmBusinessId})');
        return farm;
      },
    );
  }

  /// Update an existing farm
  Future<ServiceResult<void>> updateFarm(FarmIsar farm) async {
    return await executeWithErrorHandling(
      'update farm',
      () async {
        // Validate farm
        if (farm.farmBusinessId?.isEmpty ?? true) {
          throw ValidationException('Farm business ID is required for updates');
        }
        if (farm.name?.isEmpty ?? true) {
          throw ValidationException('Farm name is required');
        }

        // Update timestamp
        farm.updatedAt = DateTime.now();

        // Save the farm
        await _farmRepository.save(farm);

        logInfo('Updated farm: ${farm.name} (ID: ${farm.farmBusinessId})');
      },
    );
  }

  /// Delete a farm and all related data
  Future<ServiceResult<void>> deleteFarm(String farmBusinessId) async {
    return await executeWithErrorHandling(
      'delete farm',
      () async {
        if (farmBusinessId.isEmpty) {
          throw ValidationException('Farm business ID is required');
        }

        // Delete the farm
        await _farmRepository.deleteByBusinessId(farmBusinessId);

        logInfo('Deleted farm with ID: $farmBusinessId');
      },
    );
  }

  /// Get the current farm with error handling
  Future<ServiceResult<FarmIsar?>> getCurrentFarm() async {
    return await executeWithErrorHandling(
      'get current farm',
      () async {
        final farm = await _farmRepository.getCurrentFarm();
        if (farm != null) {
          logInfo('Found current farm: ${farm.name} (ID: ${farm.farmBusinessId})');
        } else {
          logInfo('No current farm found in database');
        }
        return farm;
      },
    );
  }

  //=== BREED CATEGORY OPERATIONS ===//

  /// Create a new breed category
  Future<ServiceResult<void>> createBreedCategory({
    required String name,
    required String farmBusinessId,
    String? description,
  }) async {
    return await executeWithErrorHandling(
      'create breed category',
      () async {
        if (name.isEmpty) {
          throw ValidationException('Breed category name is required');
        }
        if (farmBusinessId.isEmpty) {
          throw ValidationException('Farm business ID is required');
        }

        final category = BreedCategoryIsar.create(
          businessId: const Uuid().v4(),
          name: name,
          farmBusinessId: farmBusinessId,
          description: description,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        await _breedCategoryRepository.save(category);
        logInfo('Created breed category: $name');
      },
    );
  }

  //=== BACKUP OPERATIONS ===//

  /// Create a backup using the cloud backup service
  Future<ServiceResult<CloudBackupResult>> createBackup() async {
    return await executeWithErrorHandling(
      'create backup',
      () async {
        final farmResult = await getCurrentFarm();
        if (!farmResult.isSuccess || farmResult.data == null) {
          throw Exception('No current farm found for backup');
        }

        final farmId = farmResult.data!.farmBusinessId!;
        logInfo('Creating backup for farm: $farmId');

        // Use Google Drive as default storage provider
        final result = await _cloudBackupService.createCloudBackup(
          farmId, 
          BackupStorageProvider.googleDrive,
        );

        if (result.success) {
          logInfo('Backup created successfully');
        } else {
          logWarning('Backup failed: ${result.message}');
        }

        return result;
      },
    );
  }

  /// Restore from a backup
  Future<ServiceResult<CloudBackupResult>> restoreFromBackup(
    String backupId, 
    BackupStorageProvider provider,
  ) async {
    return await executeWithErrorHandling(
      'restore from backup',
      () async {
        logInfo('Restoring from backup: $backupId using provider: ${provider.name}');

        final result = await _cloudBackupService.restoreFromCloudBackup(backupId, provider);

        if (result.success) {
          logInfo('Backup restored successfully: $backupId');
        } else {
          logWarning('Restore failed: ${result.message}');
        }

        return result;
      },
    );
  }

  //=== PRIVATE HELPER METHODS ===//

  /// Create default categories and settings for a new farm
  Future<void> _createDefaultFarmSetup(String farmBusinessId) async {
    // Create default breed categories
    await _createDefaultBreedCategories(farmBusinessId);
    
    // Create default settings
    await _createDefaultSettings(farmBusinessId);
  }

  /// Create default breed categories
  Future<void> _createDefaultBreedCategories(String farmBusinessId) async {
    final defaultCategories = [
      'Dairy Cattle',
      'Beef Cattle',
      'Mixed Breed',
      'Local Breed',
    ];

    for (final categoryName in defaultCategories) {
      await createBreedCategory(
        name: categoryName,
        farmBusinessId: farmBusinessId,
        description: 'Default $categoryName category',
      );
    }
  }

  /// Create default settings for the farm
  Future<void> _createDefaultSettings(String farmBusinessId) async {
    // This would create default alert settings, currency settings, etc.
    // Implementation depends on the specific requirements
    logInfo('Created default settings for farm: $farmBusinessId');
  }
}

/// Custom validation exception for farm setup operations
class ValidationException implements Exception {
  final String message;
  
  ValidationException(this.message);
  
  @override
  String toString() => 'ValidationException: $message';
}
