import 'package:flutter/foundation.dart';
import '../models/event_isar.dart';

/// Performance monitoring and optimization service for Events module
class EventPerformanceService {
  static final Map<String, DateTime> _operationStartTimes = {};
  static final Map<String, List<int>> _operationDurations = {};

  /// Start timing an operation
  static void startOperation(String operationName) {
    _operationStartTimes[operationName] = DateTime.now();
  }

  /// End timing an operation and log performance
  static void endOperation(String operationName) {
    final startTime = _operationStartTimes[operationName];
    if (startTime == null) return;

    final duration = DateTime.now().difference(startTime).inMilliseconds;
    _operationDurations.putIfAbsent(operationName, () => []).add(duration);
    
    // Log slow operations
    if (duration > 1000) { // More than 1 second
      debugPrint('⚠️ SLOW OPERATION: $operationName took ${duration}ms');
    }
    
    _operationStartTimes.remove(operationName);
  }

  /// Get performance statistics for an operation
  static Map<String, dynamic> getOperationStats(String operationName) {
    final durations = _operationDurations[operationName];
    if (durations == null || durations.isEmpty) {
      return {'count': 0, 'average': 0, 'min': 0, 'max': 0};
    }

    final count = durations.length;
    final sum = durations.reduce((a, b) => a + b);
    final average = sum / count;
    final min = durations.reduce((a, b) => a < b ? a : b);
    final max = durations.reduce((a, b) => a > b ? a : b);

    return {
      'count': count,
      'average': average.round(),
      'min': min,
      'max': max,
      'total': sum,
    };
  }

  /// Get all performance statistics
  static Map<String, Map<String, dynamic>> getAllStats() {
    final stats = <String, Map<String, dynamic>>{};
    for (final operationName in _operationDurations.keys) {
      stats[operationName] = getOperationStats(operationName);
    }
    return stats;
  }

  /// Clear performance statistics
  static void clearStats() {
    _operationDurations.clear();
    _operationStartTimes.clear();
  }

  /// Log memory usage for large datasets
  static void logMemoryUsage(String context, int itemCount) {
    if (itemCount > 1000) {
      debugPrint('📊 MEMORY: $context processing $itemCount items');
    }
  }

  /// Optimize event list for display
  static List<EventIsar> optimizeEventList(List<EventIsar> events, {int? limit}) {
    startOperation('optimize_event_list');
    
    try {
      // Apply limit if specified
      final optimizedEvents = limit != null && events.length > limit
          ? events.take(limit).toList()
          : events;
      
      logMemoryUsage('Event list optimization', optimizedEvents.length);
      
      return optimizedEvents;
    } finally {
      endOperation('optimize_event_list');
    }
  }

  /// Check if operation should be cached
  static bool shouldCache(String operationName, int dataSize) {
    // Cache operations with large datasets or frequent access
    if (dataSize > 100) return true;
    
    final stats = getOperationStats(operationName);
    final count = stats['count'] as int;
    
    // Cache if operation has been called more than 5 times
    return count > 5;
  }

  /// Log performance warning
  static void logPerformanceWarning(String operation, String warning) {
    debugPrint('⚠️ PERFORMANCE WARNING [$operation]: $warning');
  }

  /// Monitor database query performance
  static void monitorQuery(String queryName, int resultCount, int durationMs) {
    if (durationMs > 500) { // More than 500ms
      logPerformanceWarning(queryName, 
        'Query took ${durationMs}ms and returned $resultCount results');
    }
    
    if (resultCount > 1000) {
      logPerformanceWarning(queryName, 
        'Query returned $resultCount results - consider pagination');
    }
  }

  /// Get performance recommendations
  static List<String> getPerformanceRecommendations() {
    final recommendations = <String>[];
    final stats = getAllStats();
    
    for (final entry in stats.entries) {
      final operationName = entry.key;
      final operationStats = entry.value;
      final average = operationStats['average'] as int;
      final count = operationStats['count'] as int;
      
      if (average > 1000 && count > 10) {
        recommendations.add(
          'Consider optimizing $operationName - average duration: ${average}ms'
        );
      }
      
      if (count > 100) {
        recommendations.add(
          'Consider caching results for $operationName - called $count times'
        );
      }
    }
    
    return recommendations;
  }

  /// Reset performance monitoring
  static void reset() {
    clearStats();
    debugPrint('🔄 Event performance monitoring reset');
  }
}
