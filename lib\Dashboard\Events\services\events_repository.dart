import 'package:isar/isar.dart';
import '../models/event_isar.dart';
import '../models/event_type_isar.dart';
import '../models/event_attachment_isar.dart';
import '../models/event_enums.dart';
import '../../../services/database/isar_service.dart';

/// Pure reactive repository for Events module database operations
/// Following the cattle/weight module pattern: Stream-only, logic-free, simple CRUD
/// Maximum purity: no error handling, no logging - exceptions bubble up naturally
class EventsRepository {
  final IsarService _isarService;

  // Public constructor with explicit dependency injection
  EventsRepository(this._isarService);

  // Getter for Isar instance
  Isar get _isar => _isarService.isar;

  //=== REACTIVE EVENT STREAMS ===//

  /// Watches all events with reactive updates
  /// The controller is responsible for all filtering and sorting
  Stream<List<EventIsar>> watchAllEvents() {
    return _isar.eventIsars.where().watch(fireImmediately: true);
  }

  /// Watches all event types with reactive updates
  /// The controller is responsible for all filtering and sorting
  Stream<List<EventTypeIsar>> watchAllEventTypes() {
    return _isar.eventTypeIsars.where().watch(fireImmediately: true);
  }

  //=== EVENT CRUD ===//

  /// Save (add or update) an event record using Isar's native upsert
  Future<void> saveEvent(EventIsar event) async {
    await _isar.writeTxn(() async {
      await _isar.eventIsars.put(event);
    });
  }

  /// Delete an event record by its Isar ID
  Future<void> deleteEvent(int eventId) async {
    await _isar.writeTxn(() async {
      await _isar.eventIsars.delete(eventId);
    });
  }

  /// Delete an event record by its business ID
  Future<void> deleteEventByBusinessId(String businessId) async {
    await _isar.writeTxn(() async {
      await _isar.eventIsars
          .filter()
          .businessIdEqualTo(businessId)
          .deleteAll();
    });
  }

  //=== EVENT TYPE CRUD ===//

  /// Save (add or update) an event type record using Isar's native upsert
  Future<void> saveEventType(EventTypeIsar eventType) async {
    await _isar.writeTxn(() async {
      await _isar.eventTypeIsars.put(eventType);
    });
  }

  /// Delete an event type record by its Isar ID
  Future<void> deleteEventType(int eventTypeId) async {
    await _isar.writeTxn(() async {
      await _isar.eventTypeIsars.delete(eventTypeId);
    });
  }

  /// Delete an event type record by its business ID
  Future<void> deleteEventTypeByBusinessId(String businessId) async {
    await _isar.writeTxn(() async {
      await _isar.eventTypeIsars
          .filter()
          .businessIdEqualTo(businessId)
          .deleteAll();
    });
  }

  //=== EVENT ATTACHMENT CRUD ===//

  /// Save (add or update) an event attachment record using Isar's native upsert
  Future<void> saveEventAttachment(EventAttachmentIsar attachment) async {
    await _isar.writeTxn(() async {
      await _isar.eventAttachmentIsars.put(attachment);
    });
  }

  /// Delete an event attachment record by its Isar ID
  Future<void> deleteEventAttachment(int attachmentId) async {
    await _isar.writeTxn(() async {
      await _isar.eventAttachmentIsars.delete(attachmentId);
    });
  }

  /// Delete an event attachment record by its business ID
  Future<void> deleteEventAttachmentByBusinessId(String businessId) async {
    await _isar.writeTxn(() async {
      await _isar.eventAttachmentIsars
          .filter()
          .businessIdEqualTo(businessId)
          .deleteAll();
    });
  }

  //=== QUERY METHODS FOR ANALYTICS ===//

  /// Get all events (for analytics and validation)
  /// Returns a Future<List> for one-time data fetching
  Future<List<EventIsar>> getAllEvents() async {
    return await _isar.eventIsars.where().findAll();
  }

  /// Get all event types (for analytics and validation)
  /// Returns a Future<List> for one-time data fetching
  Future<List<EventTypeIsar>> getAllEventTypes() async {
    return await _isar.eventTypeIsars.where().findAll();
  }

  /// Get event by business ID (for validation and navigation)
  /// Returns a Future<EventIsar?> for one-time data fetching
  Future<EventIsar?> getEventByBusinessId(String businessId) async {
    return await _isar.eventIsars
        .filter()
        .businessIdEqualTo(businessId)
        .findFirst();
  }

  /// Get event type by business ID (for validation and navigation)
  /// Returns a Future<EventTypeIsar?> for one-time data fetching
  Future<EventTypeIsar?> getEventTypeByBusinessId(String businessId) async {
    return await _isar.eventTypeIsars
        .filter()
        .businessIdEqualTo(businessId)
        .findFirst();
  }

  /// Get events for a specific cattle (for cattle-specific analytics)
  /// Returns a Future<List> for one-time data fetching
  Future<List<EventIsar>> getEventsByCattle(String cattleTagId) async {
    return await _isar.eventIsars
        .filter()
        .cattleTagIdEqualTo(cattleTagId, caseSensitive: false)
        .findAll();
  }

  //=== BACKWARD COMPATIBILITY METHODS ===//

  /// Update event - alias for saveEvent for backward compatibility
  Future<void> updateEvent(EventIsar event) async {
    await saveEvent(event);
  }

  /// Update event type - alias for saveEventType for backward compatibility
  Future<void> updateEventType(EventTypeIsar eventType) async {
    await saveEventType(eventType);
  }

  /// Update event attachment - alias for saveEventAttachment for backward compatibility
  Future<void> updateEventAttachment(EventAttachmentIsar attachment) async {
    await saveEventAttachment(attachment);
  }
}
