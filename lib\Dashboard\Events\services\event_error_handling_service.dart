import 'package:flutter/foundation.dart';

/// Error handling service for Events module
class EventErrorHandlingService {
  /// Handle and format event-related errors
  static String handleError(dynamic error, {String? context}) {
    debugPrint('🚨 EVENT ERROR ${context != null ? '($context)' : ''}: $error');
    
    if (error is Exception) {
      return _formatException(error, context);
    } else if (error is String) {
      return error;
    } else {
      return 'An unexpected error occurred${context != null ? ' while $context' : ''}';
    }
  }

  /// Format exception messages for user display
  static String _formatException(Exception exception, String? context) {
    final message = exception.toString();
    
    // Database errors
    if (message.contains('database') || message.contains('isar')) {
      return 'Database error occurred${context != null ? ' while $context' : ''}. Please try again.';
    }
    
    // Network errors
    if (message.contains('network') || message.contains('connection')) {
      return 'Network error occurred${context != null ? ' while $context' : ''}. Please check your connection.';
    }
    
    // Validation errors
    if (message.contains('validation') || message.contains('invalid')) {
      return 'Validation error: ${message.replaceAll('Exception: ', '')}';
    }
    
    // Permission errors
    if (message.contains('permission') || message.contains('unauthorized')) {
      return 'Permission denied${context != null ? ' while $context' : ''}. Please check your access rights.';
    }
    
    // File system errors
    if (message.contains('file') || message.contains('directory')) {
      return 'File system error occurred${context != null ? ' while $context' : ''}. Please try again.';
    }
    
    // Generic error
    return message.replaceAll('Exception: ', '');
  }

  /// Handle specific event operation errors
  static String handleEventOperationError(dynamic error, String operation) {
    final baseMessage = handleError(error, context: operation);
    
    switch (operation.toLowerCase()) {
      case 'saving event':
      case 'creating event':
        return 'Failed to save event: $baseMessage';
      case 'updating event':
        return 'Failed to update event: $baseMessage';
      case 'deleting event':
        return 'Failed to delete event: $baseMessage';
      case 'completing event':
        return 'Failed to complete event: $baseMessage';
      case 'loading events':
        return 'Failed to load events: $baseMessage';
      case 'filtering events':
        return 'Failed to filter events: $baseMessage';
      default:
        return baseMessage;
    }
  }

  /// Handle validation errors
  static String handleValidationErrors(Map<String, String> validationErrors) {
    if (validationErrors.isEmpty) {
      return 'Validation failed';
    }
    
    if (validationErrors.length == 1) {
      return validationErrors.values.first;
    }
    
    // Multiple validation errors
    final errorList = validationErrors.values.take(3).join(', ');
    final remaining = validationErrors.length - 3;
    
    if (remaining > 0) {
      return '$errorList and $remaining more error${remaining > 1 ? 's' : ''}';
    }
    
    return errorList;
  }

  /// Check if error is recoverable
  static bool isRecoverableError(dynamic error) {
    final message = error.toString().toLowerCase();
    
    // Non-recoverable errors
    if (message.contains('permission') || 
        message.contains('unauthorized') ||
        message.contains('forbidden')) {
      return false;
    }
    
    // Recoverable errors (network, temporary database issues, etc.)
    return true;
  }

  /// Get retry suggestion for error
  static String getRetrySuggestion(dynamic error) {
    final message = error.toString().toLowerCase();
    
    if (message.contains('network') || message.contains('connection')) {
      return 'Please check your internet connection and try again.';
    }
    
    if (message.contains('database') || message.contains('isar')) {
      return 'Please wait a moment and try again.';
    }
    
    if (message.contains('validation')) {
      return 'Please correct the highlighted fields and try again.';
    }
    
    if (message.contains('permission')) {
      return 'Please contact your administrator for access.';
    }
    
    return 'Please try again in a few moments.';
  }
}
