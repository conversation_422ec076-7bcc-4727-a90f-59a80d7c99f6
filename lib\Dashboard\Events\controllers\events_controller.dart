import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:isar/isar.dart';

import '../models/event_isar.dart';
import '../models/event_type_isar.dart';
import '../models/event_attachment_isar.dart';
import '../models/event_enums.dart';
import '../services/events_repository.dart';
import '../services/events_analytics_service.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../Cattle/services/cattle_repository.dart';
import '../../Cattle/controllers/cattle_controller.dart'; // Import for ControllerState enum

/// Filter state for Events module
class EventFilterState {
  final String? searchQuery;
  final DateTime? startDate;
  final DateTime? endDate;
  final EventCategory? category;
  final EventStatus? status;
  final EventPriority? priority;
  final String? cattleTagId;
  final bool? isRecurring;
  final bool? isCompleted;

  const EventFilterState({
    this.searchQuery,
    this.startDate,
    this.endDate,
    this.category,
    this.status,
    this.priority,
    this.cattleTagId,
    this.isRecurring,
    this.isCompleted,
  });

  /// Empty filter state
  static const EventFilterState empty = EventFilterState();

  /// Check if filter has any active filters
  bool get hasActiveFilters {
    return (searchQuery?.isNotEmpty ?? false) ||
           startDate != null ||
           endDate != null ||
           category != null ||
           status != null ||
           priority != null ||
           (cattleTagId?.isNotEmpty ?? false) ||
           isRecurring != null ||
           isCompleted != null;
  }
}

/// Events Controller
///
/// Manages state for the events system following the exact Cattle controller pattern.
/// Implements dual-stream architecture with filtered/unfiltered data separation.
/// Analytics are ALWAYS calculated on unfiltered data for accuracy.
///
/// Follows the cattle controller pattern exactly for consistency.
class EventsController extends ChangeNotifier {
  // Use lazy getters to avoid accessing GetIt services in constructor
  EventsRepository get _eventsRepository => GetIt.instance<EventsRepository>();
  CattleRepository get _cattleRepository => GetIt.instance<CattleRepository>();
  Isar get _isar => GetIt.instance<Isar>();

  // State management
  ControllerState _state = ControllerState.loading;
  String? _errorMessage;

  // Stream subscriptions for real-time updates - Separated for filtered/unfiltered data
  StreamSubscription<List<EventIsar>>? _unfilteredEventSubscription;
  StreamSubscription<List<EventTypeIsar>>? _eventTypesSubscription;
  StreamSubscription<List<CattleIsar>>? _cattleSubscription;
  StreamSubscription<List<EventIsar>>? _filteredEventSubscription;

  // Data - Critical separation: filtered data for UI, unfiltered for analytics
  List<EventIsar> _unfilteredEvents = []; // Complete dataset for analytics calculations
  List<EventIsar> _filteredEvents = []; // Filtered dataset for UI display
  List<EventTypeIsar> _eventTypes = [];
  List<CattleIsar> _cattle = [];

  // Current filter settings
  EventFilterState _currentFilter = EventFilterState.empty;
  bool _hasActiveFilters = false; // Track if filters are currently applied

  // Analytics result from the dedicated service - ALWAYS calculated on unfiltered data
  EventAnalyticsResult _analyticsResult = EventAnalyticsResult.empty;

  // Getters
  ControllerState get state => _state;
  String? get errorMessage => _errorMessage;
  List<EventIsar> get events => _filteredEvents;
  List<EventTypeIsar> get eventTypes => _eventTypes;
  List<CattleIsar> get cattle => _cattle;
  EventFilterState get currentFilter => _currentFilter;
  bool get hasActiveFilters => _hasActiveFilters;
  EventAnalyticsResult get analytics => _analyticsResult;

  // Constructor
  EventsController() {
    _initializeStreamListeners();
  }

  // Initialize and load data
  Future<void> loadData() async {
    try {
      _setState(ControllerState.loading);
      // Data comes from Isar watch() streams automatically
      // State management is handled by _handleStreamUpdate
    } catch (e, stackTrace) {
      debugPrint('Error loading events data: $e\n$stackTrace');
      _setError('Failed to load events data: ${e.toString()}');
    }
  }

  /// Initialize stream listeners for real-time updates using Isar's native watch
  /// Critical: Separate streams for unfiltered (analytics) data
  void _initializeStreamListeners() {
    // Primary stream: Unfiltered data for analytics calculations
    // This stream NEVER changes and always provides the complete dataset
    _unfilteredEventSubscription = _isar.eventIsars.where()
        .watch(fireImmediately: true)
        .listen((unfilteredList) {
      _unfilteredEvents = unfilteredList;
      _updateAnalytics();
      
      // Update filtered data if no active filters
      if (!_hasActiveFilters) {
        _filteredEvents = List.from(_unfilteredEvents);
      }
      
      _setState(ControllerState.loaded);
    });

    // Event types stream
    _eventTypesSubscription = _isar.eventTypeIsars.where()
        .watch(fireImmediately: true)
        .listen((eventTypes) {
      _eventTypes = eventTypes;
      notifyListeners();
    });

    // Cattle stream for analytics
    _cattleSubscription = _isar.cattleIsars.where()
        .watch(fireImmediately: true)
        .listen((cattle) {
      _cattle = cattle;
      _updateAnalytics();
    });
  }

  /// Update analytics - ALWAYS calculated on unfiltered data
  void _updateAnalytics() {
    _analyticsResult = EventAnalyticsService.calculate(_unfilteredEvents, _cattle);
  }

  /// Apply filters at the database level for ultimate scalability
  /// Critical Fix: This method now creates a separate filtered stream without affecting analytics
  Future<void> applyFilters(EventFilterState? filters) async {
    // Cancel existing filtered subscription (but keep unfiltered stream for analytics)
    _filteredEventSubscription?.cancel();

    // Update filter state
    _currentFilter = filters ?? EventFilterState.empty;
    _hasActiveFilters = _currentFilter.hasActiveFilters;

    if (_hasActiveFilters) {
      // Build filtered query for UI display
      final filteredQuery = _buildFilteredQuery(_currentFilter);

      // Create separate stream for filtered data
      _filteredEventSubscription = filteredQuery.watch(fireImmediately: true)
          .listen((filteredList) {
        _handleFilteredDataUpdate(filteredList);
      });
    } else {
      // No filters: filtered data equals unfiltered data
      _filteredEvents = List.from(_unfilteredEvents);
      _setState(ControllerState.loaded);
      notifyListeners();
    }
  }

  /// Handle filtered data update
  void _handleFilteredDataUpdate(List<EventIsar> filteredList) {
    _filteredEvents = filteredList;
    _setState(ControllerState.loaded);
    notifyListeners();
  }

  /// Build filtered query for database-level filtering
  /// Following the exact Cattle module pattern for consistency
  QueryBuilder<EventIsar, EventIsar, QAfterWhereClause> _buildFilteredQuery(EventFilterState filters) {
    var currentQuery = _isar.eventIsars.where();

    // Apply search filter at database level
    if (filters.searchQuery?.isNotEmpty ?? false) {
      final searchTerm = filters.searchQuery!.toLowerCase();
      currentQuery = currentQuery.filter().group((q) => q
          .titleContains(searchTerm, caseSensitive: false)
          .or()
          .descriptionContains(searchTerm, caseSensitive: false)
          .or()
          .cattleTagIdContains(searchTerm, caseSensitive: false));
    }

    // Apply date range filters at database level
    if (filters.startDate != null) {
      currentQuery = currentQuery.filter().scheduledDateGreaterThan(filters.startDate!);
    }
    if (filters.endDate != null) {
      // Add one day to make end date inclusive
      final inclusiveEndDate = filters.endDate!.add(const Duration(days: 1));
      currentQuery = currentQuery.filter().scheduledDateLessThan(inclusiveEndDate);
    }

    // Apply category filter at database level
    if (filters.category != null) {
      currentQuery = currentQuery.filter().categoryEqualTo(filters.category!);
    }

    // Apply status filter at database level
    if (filters.status != null) {
      currentQuery = currentQuery.filter().statusEqualTo(filters.status!);
    }

    // Apply priority filter at database level
    if (filters.priority != null) {
      currentQuery = currentQuery.filter().priorityEqualTo(filters.priority!);
    }

    // Apply cattle filter at database level
    if (filters.cattleTagId?.isNotEmpty ?? false) {
      currentQuery = currentQuery.filter().cattleTagIdEqualTo(filters.cattleTagId!);
    }

    // Apply recurring filter at database level
    if (filters.isRecurring != null) {
      currentQuery = currentQuery.filter().isRecurringEqualTo(filters.isRecurring!);
    }

    // Apply completion filter at database level
    if (filters.isCompleted != null) {
      currentQuery = currentQuery.filter().isCompletedEqualTo(filters.isCompleted!);
    }

    // Apply sorting - default to scheduled date ascending
    currentQuery = currentQuery.sortByScheduledDate();

    return currentQuery;
  }

  /// Clear all filters
  Future<void> clearFilters() async {
    await applyFilters(EventFilterState.empty);
  }

  /// Refresh all data
  Future<void> refresh() async {
    try {
      // Force analytics recalculation on unfiltered data
      _updateAnalytics();
      notifyListeners();
    } catch (e, stackTrace) {
      debugPrint('Error refreshing events data: $e\n$stackTrace');
      throw Exception('Failed to refresh events data: ${e.toString()}');
    }
  }

  // State management helpers
  void _setState(ControllerState newState) {
    _state = newState;
    _errorMessage = null;
    notifyListeners();
  }

  void _setError(String error) {
    _state = ControllerState.error;
    _errorMessage = error;
    notifyListeners();
  }

  //=== CRUD OPERATIONS ===//

  /// Add new event
  Future<void> addEvent(EventIsar event) async {
    await _eventsRepository.saveEvent(event);
    // Stream will automatically update the UI
  }

  /// Update existing event
  Future<void> updateEvent(EventIsar event) async {
    await _eventsRepository.saveEvent(event);
    // Stream will automatically update the UI
  }

  /// Delete event
  Future<void> deleteEvent(String businessId) async {
    await _eventsRepository.deleteEventByBusinessId(businessId);
    // Stream will automatically update the UI
  }

  /// Complete event
  Future<void> completeEvent(String businessId, {
    String? completedBy,
    String? completionNotes,
    double? actualCost,
  }) async {
    // Find the event in unfiltered data
    final event = _unfilteredEvents.firstWhere(
      (e) => e.businessId == businessId,
      orElse: () => throw Exception('Event not found'),
    );

    // Mark as completed
    event.markCompleted(
      completedBy: completedBy,
      completionNotes: completionNotes,
      actualCost: actualCost,
    );

    // Save the updated event
    await _eventsRepository.saveEvent(event);
    // Stream will automatically update the UI
  }

  /// Add new event type
  Future<void> addEventType(EventTypeIsar eventType) async {
    await _eventsRepository.saveEventType(eventType);
    // Stream will automatically update the UI
  }

  /// Update existing event type
  Future<void> updateEventType(EventTypeIsar eventType) async {
    await _eventsRepository.saveEventType(eventType);
    // Stream will automatically update the UI
  }

  /// Delete event type
  Future<void> deleteEventType(String businessId) async {
    await _eventsRepository.deleteEventTypeByBusinessId(businessId);
    // Stream will automatically update the UI
  }

  /// Add new event attachment
  Future<void> addEventAttachment(EventAttachmentIsar attachment) async {
    await _eventsRepository.saveEventAttachment(attachment);
    // Stream will automatically update the UI
  }

  /// Delete event attachment
  Future<void> deleteEventAttachment(String businessId) async {
    await _eventsRepository.deleteEventAttachmentByBusinessId(businessId);
    // Stream will automatically update the UI
  }

  @override
  void dispose() {
    // Cancel all stream subscriptions to prevent memory leaks
    _unfilteredEventSubscription?.cancel();
    _eventTypesSubscription?.cancel();
    _cattleSubscription?.cancel();
    _filteredEventSubscription?.cancel();
    super.dispose();
  }
}
